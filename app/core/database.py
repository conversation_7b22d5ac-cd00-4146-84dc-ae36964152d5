"""
DuckDB connection management.
"""

import duckdb
import os
import logging
from contextlib import contextmanager
from typing import Generator

from app.core.config import settings

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages DuckDB connections and spatial extension loading."""

    def __init__(self):
        self._connection = None
        self._setup_complete = False

    def get_connection(self) -> duckdb.DuckDBPyConnection:
        """Get or create a DuckDB connection."""
        if self._connection is None:
            self._connection = duckdb.connect(settings.duckdb_path)
            self._setup_database()
        return self._connection

    def _setup_database(self):
        """Setup database with spatial extension and data loading."""
        if self._setup_complete:
            return

        conn = self._connection

        # Install and load spatial extension
        conn.execute("INSTALL spatial;")
        conn.execute("LOAD spatial;")

        # Optimize DuckDB performance settings from config
        conn.execute(f"SET threads TO {settings.duckdb_config['threads']};")
        conn.execute(f"SET memory_limit = '{settings.duckdb_config['memory_limit']}';")
        conn.execute(f"SET max_memory = '{settings.duckdb_config['max_memory']}';")

        # Create views for data files if they exist
        self._create_data_views(conn)

        self._setup_complete = True

    def _create_data_views(self, conn: duckdb.DuckDBPyConnection):
        """Create views for data files using configuration."""

        # Create views for NPL data from GPKG
        if os.path.exists(settings.gpkg_file_path):
            for view_key, layer_name in settings.gpkg_layers.items():
                try:
                    conn.execute(f"""
                        CREATE OR REPLACE VIEW {view_key} AS
                        SELECT * FROM st_read('{settings.gpkg_file_path}', layer='{layer_name}')
                    """)
                    logger.info(f"Created GPKG view: {view_key} from layer {layer_name}")
                except Exception as e:
                    logger.error(f"Failed to create GPKG view {view_key}: {e}")
        else:
            logger.warning(f"GPKG file not found: {settings.gpkg_file_path}")

        # Create views for legacy parquet data files
        for view_key, file_path in settings.data_files.items():
            if os.path.exists(file_path):
                try:
                    conn.execute(f"""
                        CREATE OR REPLACE VIEW {view_key} AS
                        SELECT * FROM read_parquet('{file_path}')
                    """)
                    logger.info(f"Created parquet view: {view_key}")
                except Exception as e:
                    logger.error(f"Failed to create parquet view {view_key}: {e}")
            else:
                logger.warning(f"File not found: {file_path}")

        # Create additional views from view definitions
        for view_name, view_sql in settings.view_definitions.items():
            try:
                conn.execute(f"CREATE OR REPLACE VIEW {view_name} AS {view_sql}")
                logger.info(f"Created view: {view_name}")
            except Exception as e:
                logger.error(f"Failed to create view {view_name}: {e}")

        # Swiss boundaries view - try to load from parquet first, then GPKG
        if os.path.exists(settings.swissboundaries_path):
            try:
                conn.execute(f"""
                    CREATE OR REPLACE VIEW swiss_municipalities AS
                    SELECT * FROM read_parquet('{settings.swissboundaries_path}')
                """)
                logger.info("Created view: swiss_municipalities (from parquet)")
            except Exception as e:
                logger.error(f"Failed to create swiss_municipalities view from parquet: {e}")
        else:
            # Try to load from GPKG file - check multiple possible locations
            gpkg_paths = [
                "data/input/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg",
                "raw_input_data/821838/swissBOUNDARIES3D_1_5_LV95_LN02.gpkg"
            ]

            gpkg_found = False
            for gpkg_path in gpkg_paths:
                if os.path.exists(gpkg_path):
                    try:
                        # Create optimized swiss_municipalities table with spatial indexes
                        conn.execute(f"""
                            CREATE OR REPLACE TABLE swiss_municipalities AS
                            SELECT
                                name as tlm_hoheitsgebiet_name,
                                bfs_nummer,
                                hist_nr,
                                geom as geometry,
                                ST_Envelope(geom) as bbox
                            FROM ST_Read('{gpkg_path}', layer='tlm_hoheitsgebiet')
                            WHERE name IS NOT NULL
                        """)

                        # Create spatial index on geometry column
                        try:
                            conn.execute("CREATE INDEX idx_swiss_muni_geom ON swiss_municipalities USING RTREE(geometry);")
                            conn.execute("CREATE INDEX idx_swiss_muni_bbox ON swiss_municipalities USING RTREE(bbox);")
                            logger.info("Created spatial indexes on swiss_municipalities")
                        except Exception as idx_e:
                            logger.warning(f"Could not create spatial indexes: {idx_e}")

                        logger.info(f"Created optimized table: swiss_municipalities (from GPKG: {gpkg_path})")
                        gpkg_found = True
                        break
                    except Exception as e:
                        logger.error(f"Failed to create swiss_municipalities table from GPKG {gpkg_path}: {e}")
                        continue

            if not gpkg_found:
                logger.warning("Swiss boundaries file not found in any expected location, creating dummy view")
                self._create_dummy_municipalities_view(conn)

    def _create_dummy_municipalities_view(self, conn: duckdb.DuckDBPyConnection):
        """Create a dummy municipalities view for testing."""
        try:
            conn.execute("""
                CREATE OR REPLACE VIEW swiss_municipalities AS
                SELECT
                    'Test Municipality' as tlm_hoheitsgebiet_name,
                    ST_GeomFromText('POLYGON((8.5 47.0, 8.6 47.0, 8.6 47.1, 8.5 47.1, 8.5 47.0))') as geometry
                UNION ALL
                SELECT
                    'Another Municipality' as tlm_hoheitsgebiet_name,
                    ST_GeomFromText('POLYGON((8.6 47.0, 8.7 47.0, 8.7 47.1, 8.6 47.1, 8.6 47.0))') as geometry
            """)
            logger.info("Created dummy swiss_municipalities view")
        except Exception as e:
            logger.error(f"Failed to create dummy municipalities view: {e}")

    def close(self):
        """Close the database connection."""
        if self._connection:
            self._connection.close()
            self._connection = None
            self._setup_complete = False

# Global database manager instance
db_manager = DatabaseManager()

@contextmanager
def get_db() -> Generator[duckdb.DuckDBPyConnection, None, None]:
    """Context manager for database connections."""
    conn = db_manager.get_connection()
    try:
        yield conn
    finally:
        # Connection is reused, so we don't close it here
        pass

def get_db_dependency() -> duckdb.DuckDBPyConnection:
    """FastAPI dependency for database connections."""
    return db_manager.get_connection()
