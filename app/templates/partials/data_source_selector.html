<!-- Data Source Selector -->
<div class="data-source-selector">
    <div class="selector-header">
        <div class="selector-summary">
            {{ data_sources.available_count }} verfügbar, {{ data_sources.unavailable_count }} geplant
        </div>
    </div>

    <!-- Available Sources Section -->
    <div class="available-sources-section">
        <div class="data-source-list available-only">
            {% for source in data_sources.sources %}
            {% if source.is_available %}
            <div class="data-source-item available">
                <div class="source-main">
                    <input
                        type="radio"
                        name="data_source"
                        id="source_{{ source.local.id if source.local else source.legislative.id }}"
                        value="{{ source.local.id if source.local else source.legislative.id }}"

                        hx-get="/api/municipalities/selector"
                        hx-target="#municipality-selector"
                        hx-trigger="change"
                        hx-include="[name='data_source']:checked"
                        hx-vals='{"data_source": "{{ source.local.id if source.local else source.legislative.id }}"}'
                        onchange="handleDataSourceChange('{{ source.local.id if source.local else source.legislative.id }}', this.checked)"
                    >
                    <label
                        for="source_{{ source.local.id if source.local else source.legislative.id }}"
                        class="source-label"
                    >
                        <span class="source-name">{{ source.display_name }}</span>
                        {% if source.canton_summary %}
                        <span class="status-badge canton-issues" title="Kantone mit unvollständigen oder fehlenden Daten">{{ source.canton_summary }}</span>
                        {% else %}
                        <span class="status-badge available">Vollständig verfügbar</span>
                        {% endif %}
                    </label>

                    <!-- WMS Opacity Slider (only for Nutzungsplanung sources) -->
                    {% if (source.local.id if source.local else source.legislative.id) == '820737' %}
                    <div class="wms-opacity-control">
                        <label for="wms-opacity-slider" class="opacity-label">WMS:</label>
                        <input
                            type="range"
                            id="wms-opacity-slider"
                            class="opacity-slider"
                            min="0"
                            max="100"
                            value="60"
                            title="WMS Layer Transparenz"
                            oninput="updateWMSOpacity(this.value)"
                        >
                        <span class="opacity-value">60%</span>
                    </div>
                    {% endif %}

                    <button
                        type="button"
                        class="info-button"
                        title="Informationen anzeigen"
                        hx-get="/api/data-sources/{{ source.local.id if source.local else source.legislative.id }}/info"
                        hx-target="#data-source-info-modal .modal-body"
                        hx-trigger="click"
                        _="on click remove .hidden from #data-source-info-modal"
                    >
                        ℹ️
                    </button>
                </div>

                {% if source.description %}
                <div class="source-description">
                    {{ source.description }}
                </div>
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>

    <!-- Planned Sources Section -->
    <div class="planned-sources-section">
        <h5>Geplante Datenquellen
            <span
                class="toggle-planned"
                _="on click toggle .hidden on #planned-sources-list"
            >
                ▼ Anzeigen ({{ data_sources.unavailable_count }})
            </span>
        </h5>
        <div id="planned-sources-container">
            <div class="data-source-list planned-sources hidden" id="planned-sources-list">
            {% for source in data_sources.sources %}
            {% if not source.is_available %}
            <div class="data-source-item unavailable">
                <div class="source-main">
                    <input
                        type="checkbox"
                        name="data_source"
                        id="source_{{ source.legislative.id }}"
                        value="{{ source.legislative.id }}"
                        disabled
                    >
                    <label
                        for="source_{{ source.legislative.id }}"
                        class="source-label disabled"
                    >
                        <span class="source-name">{{ source.display_name }}</span>
                        <span class="status-badge unavailable">Geplant</span>
                    </label>

                    <button
                        type="button"
                        class="info-button"
                        title="Informationen anzeigen"
                        hx-get="/api/data-sources/{{ source.legislative.id }}/info"
                        hx-target="#data-source-info-modal .modal-body"
                        hx-trigger="click"
                        _="on click remove .hidden from #data-source-info-modal"
                    >
                        ℹ️
                    </button>
                </div>

                {% if source.description %}
                <div class="source-description">
                    {{ source.description }}
                </div>
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
// Initialize data source selections on page load
document.addEventListener('DOMContentLoaded', function() {
    // Check for initially selected data source (radio button)
    const checkedSource = document.querySelector('input[name="data_source"]:checked');
    if (checkedSource && typeof window.handleDataSourceChange === 'function') {
        window.handleDataSourceChange(checkedSource.value, true);
    }
});
</script>

<!-- Data Source Info Modal -->
<div id="data-source-info-modal" class="modal hidden" _="on click if target is me add .hidden to me">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Datenquellen-Information</h3>
            <button
                type="button"
                class="close-button"
                _="on click add .hidden to #data-source-info-modal"
            >
                &times;
            </button>
        </div>
        <div class="modal-body">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
