{% if zones %}
<!-- API Query Link -->
<div class="api-query-info">
    <p class="api-query-label">API Query:</p>
    <a href="{{ api_query_url }}" target="_blank" class="api-query-link">{{ api_query_url }}</a>
</div>

<table class="zones-table">
    <thead>
        <tr>
            <th>Zone Name</th>
            <th>Type</th>
            <th>Description</th>
            <th>Area (m²)</th>
            <th>Canton</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for zone in zones %}
        <tr>
            <td>{{ zone.zone_name or 'N/A' }}</td>
            <td>{{ zone.zone_type.value if zone.zone_type else 'N/A' }}</td>
            <td>{{ zone.description or 'N/A' }}</td>
            <td class="area-value">{{ "{:.0f}".format(zone.area) if zone.area else 'N/A' }}</td>
            <td>{{ zone.properties.canton if zone.properties and zone.properties.canton else 'N/A' }}</td>
            <td>
                <button
                    type="button"
                    class="btn-small"
                    onclick="showZoneDetails('{{ zone.id }}')"
                >
                    Details
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<div class="zones-summary">
    <p><strong><span class="count-value">{{ zones|length }}</span> zones found</strong>
    {% if municipality_id %}for {{ municipality_id }}{% endif %}
    {% if zone_type %}({{ zone_type }}){% endif %}</p>

    {% if zones|length > 0 %}
    <p><strong>Total Area:</strong> <span class="area-value">{{ "{:.0f}".format(zones|sum(attribute='area') if zones|selectattr('area')|list else 0) }} m²</span></p>
    {% endif %}
</div>

<!-- Update the results summary -->
<script>
    document.getElementById('results-summary').innerHTML = '<span class="count-value">{{ zones|length }}</span> zones found';

    // Load zones for municipality
    {% if municipality_id %}
    console.log('Loading zones for municipality: {{ municipality_id }}');

    // Set the current municipality ID for the map
    if (typeof window !== 'undefined') {
        window.currentMunicipalityId = '{{ municipality_id }}';
    }

    // Show loading indicator
    if (typeof window.showMapLoading === 'function') {
        window.showMapLoading();
    }

    // Trigger HTMX event to load zones
    htmx.trigger('#map', 'load-zones', {
        municipality: '{{ municipality_id }}',
        zoneType: '{{ zone_type or "" }}'
    });
    {% else %}
    console.log('No municipality_id provided, skipping map update');
    {% endif %}
</script>

{% else %}
<div class="no-results-container">
    <p class="no-results">No zones found for the selected municipality and filters.</p>
    {% if municipality_id %}
    <p class="no-results-help">
        This could mean:
        <br>• The municipality "{{ municipality_id }}" has no zone data in our database
        <br>• Try selecting a different municipality or zone type
        <br>• Some municipalities may use different naming conventions
    </p>
    {% endif %}
</div>

<script>
    document.getElementById('results-summary').innerHTML = '<span class="count-value">0</span> zones found';
</script>
{% endif %}
