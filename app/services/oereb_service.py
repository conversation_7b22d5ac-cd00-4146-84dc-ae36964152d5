"""
ÖREB (Öffentlich-rechtliche Eigentumsbeschränkungen) service for property restriction lookups.
"""

import httpx
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional
from fastapi import HTTPException
import logging
import base64
import time
from app.config.oereb_config import CANTONAL_OEREB_SERVICES, SPECIAL_ENDPOINTS
from app.config.canton_test_data import KNOWN_WORKING_EGRIDS

logger = logging.getLogger(__name__)

class OEREBService:
    """Service for ÖREB property restriction lookups."""

    def __init__(self):
        self.timeout = 30.0
        # Import known working EGRIDs from shared config
        self.known_working_egrids = KNOWN_WORKING_EGRIDS

    def get_cantonal_urls(self, canton: str, egrid: str) -> Dict[str, str]:
        """
        Generate the correct URLs for all formats (XML, PDF, JSON, URL) for a specific canton and EGRID.

        Args:
            canton: Canton abbreviation (e.g., 'ZH', 'BE')
            egrid: Property identifier (EGRID)

        Returns:
            Dict with keys 'xml', 'pdf', 'json', 'url' containing the correct URLs
        """
        # Generate timestamp for cantons that need it (like BL)
        timestamp = str(int(time.time() * 1000))

        # Check if canton has special URL patterns
        if canton in SPECIAL_ENDPOINTS:
            urls = {}
            for format_type, url_pattern in SPECIAL_ENDPOINTS[canton].items():
                urls[format_type] = url_pattern.format(egrid=egrid, timestamp=timestamp)
            return urls

        # Use standard URL pattern for cantons not in special endpoints
        base_url = CANTONAL_OEREB_SERVICES.get(canton)
        if not base_url:
            return {}

        return {
            'xml': f"{base_url}/extract/xml/?EGRID={egrid}&LANG=de&WITHIMAGES=true",
            'pdf': f"{base_url}/extract/pdf/?EGRID={egrid}&LANG=de",
            'json': f"{base_url}/extract/json/?EGRID={egrid}&LANG=de",
            'url': f"{base_url}/extract/url/?EGRID={egrid}&LANG=de"
        }

    async def get_egrid_from_coordinates(self, x: float, y: float) -> Optional[Dict[str, Any]]:
        """
        Get EGRID (property identifier) from coordinates using maps.geo.admin.ch API.

        This method tries to find a property at the given coordinates. If the coordinate
        lookup fails or returns an EGRID from a different canton than expected, it will
        fall back to using known working EGRIDs for testing purposes.

        Args:
            x: X coordinate in EPSG:2056 (Swiss LV95)
            y: Y coordinate in EPSG:2056 (Swiss LV95)

        Returns:
            Dict containing EGRID and property information, or None if not found
        """
        # Add detailed logging to debug coordinate issues
        logger.info(f"🔍 EGRID lookup requested for coordinates: x={x}, y={y}")

        # Validate coordinates are in Swiss bounds
        if not (2485000 <= x <= 2834000 and 1075000 <= y <= 1296000):
            logger.warning(f"⚠️ Coordinates {x}, {y} are outside Swiss bounds (EPSG:2056)")

        try:
            # Try primary layer first (official survey)
            result = await self._try_egrid_lookup_with_layer(x, y, 'all:ch.swisstopo-vd.amtliche-vermessung')
            if result:
                return result

            # Fallback to geodienste.ch WMS for areas where official survey has no data (e.g., Neuchâtel)
            logger.info(f"Primary layer failed for coordinates {x}, {y}, trying geodienste.ch WMS fallback")
            result = await self._try_geodienste_wms_fallback(x, y)
            if result:
                return result

            # If both methods fail
            logger.warning(f"No EGRID found for coordinates {x}, {y} with any method")
            return None

        except httpx.TimeoutException:
            logger.error(f"Timeout while fetching EGRID for coordinates {x}, {y}")
            raise HTTPException(status_code=408, detail="Zeitüberschreitung beim Abrufen der Grundstücksinformationen")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error while fetching EGRID: {e}")
            raise HTTPException(status_code=502, detail="Fehler beim Abrufen der Grundstücksinformationen")
        except Exception as e:
            logger.error(f"Unexpected error while fetching EGRID: {e}")
            raise HTTPException(status_code=500, detail="Unerwarteter Fehler beim Abrufen der Grundstücksinformationen")

    async def _try_egrid_lookup_with_layer(self, x: float, y: float, layer: str) -> Optional[Dict[str, Any]]:
        """
        Try EGRID lookup with a specific layer.

        Args:
            x: X coordinate in EPSG:2056
            y: Y coordinate in EPSG:2056
            layer: Layer name for the identify service

        Returns:
            Dict containing EGRID and property information, or None if not found
        """
        logger.debug(f"EGRID lookup for coordinates {x}, {y} using layer {layer}")

        url = "https://api3.geo.admin.ch/rest/services/api/MapServer/identify"
        params = {
            'geometry': f'{x},{y}',
            'geometryType': 'esriGeometryPoint',
            'layers': layer,
            'tolerance': 1,  # Precise tolerance to avoid cross-canton results
            'returnGeometry': False,
            'mapExtent': f'{x-10},{y-10},{x+10},{y+10}',
            'imageDisplay': '1,1,96',
            'sr': 2056,
            'f': 'json'
        }

        async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
            response = await client.get(url, params=params)
            response.raise_for_status()

            data = response.json()
            logger.debug(f"API response from {layer}: {len(data.get('results', []))} results")

            # Extract EGRID from response
            if 'results' in data and data['results']:
                for result in data['results']:
                    if 'attributes' in result:
                        attrs = result['attributes']
                        # Try different EGRID field names
                        egrid = attrs.get('egris_egrid') or attrs.get('EGRID') or attrs.get('egrid')
                        if egrid and egrid.strip():
                            municipality = attrs.get('GEMEINDE', attrs.get('ggdename', ''))
                            canton = attrs.get('ak', attrs.get('KANTON', ''))

                            logger.info(f"Found EGRID {egrid} for coordinates {x}, {y}, canton: {canton}, municipality: {municipality}")

                            return {
                                'egrid': egrid,
                                'municipality': municipality,
                                'canton': canton,
                                'parcel_number': attrs.get('number', attrs.get('NUMMER', '')),
                                'bfs_number': attrs.get('bfsnr', attrs.get('ggdenr')),
                                'coordinates': {'x': x, 'y': y}
                            }

                # If we have results but no EGRID, log available attributes for debugging
                if data['results']:
                    available_attrs = list(data['results'][0].get('attributes', {}).keys())
                    logger.debug(f"No EGRID found in {layer} results. Available attributes: {available_attrs}")

        return None

    async def _try_geodienste_wms_fallback(self, x: float, y: float) -> Optional[Dict[str, Any]]:
        """
        Fallback EGRID lookup using geodienste.ch WMS service.
        This is used when the official survey layer has no data (e.g., Neuchâtel).

        Args:
            x: X coordinate in EPSG:2056
            y: Y coordinate in EPSG:2056

        Returns:
            Dict containing EGRID and property information, or None if not found
        """
        logger.debug(f"Trying geodienste.ch WMS fallback for coordinates {x}, {y}")

        # Use geodienste.ch WMS GetFeatureInfo request
        url = "https://geodienste.ch/db/av_situationsplan_oereb_0/deu"
        params = {
            'SERVICE': 'WMS',
            'VERSION': '1.3.0',
            'REQUEST': 'GetFeatureInfo',
            'LAYERS': 'av_situationsplan_oereb',
            'QUERY_LAYERS': 'av_situationsplan_oereb',
            'CRS': 'EPSG:2056',
            'BBOX': f'{x-10},{y-10},{x+10},{y+10}',
            'WIDTH': '100',
            'HEIGHT': '100',
            'I': '50',  # X pixel coordinate
            'J': '50',  # Y pixel coordinate
            'INFO_FORMAT': 'application/vnd.ogc.gml',  # Use GML format since JSON is not supported
            'FEATURE_COUNT': '10'
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()

                # Parse GML response to extract EGRIS_EGRID
                gml_content = response.text
                logger.debug(f"Geodienste.ch GML response length: {len(gml_content)}")

                # Look for EGRIS_EGRID in the GML content
                import re
                egrid_pattern = r'<EGRIS_EGRID[^>]*>([^<]+)</EGRIS_EGRID>'
                egrid_match = re.search(egrid_pattern, gml_content)

                if egrid_match:
                    egrid = egrid_match.group(1).strip()
                    if egrid:
                        # Try to extract municipality and canton from GML
                        municipality = self._extract_from_gml(gml_content, 'GEMEINDE') or ''
                        canton = self._extract_from_gml(gml_content, 'KANTON')

                        # If canton not found in GML, derive from coordinates as fallback
                        if not canton:
                            canton = self._derive_canton_from_coordinates(x, y)
                            logger.info(f"Canton not found in GML, derived from coordinates: {canton}")

                        logger.info(f"Found EGRID {egrid} via geodienste.ch WMS for coordinates {x}, {y}, canton: {canton}")

                        return {
                            'egrid': egrid,
                            'municipality': municipality,
                            'canton': canton,
                            'parcel_number': self._extract_from_gml(gml_content, 'NUMMER') or '',
                            'bfs_number': None,
                            'coordinates': {'x': x, 'y': y}
                        }
                else:
                    logger.debug(f"No EGRIS_EGRID found in geodienste.ch GML response")
                    # Log a sample of the response for debugging
                    sample = gml_content[:500] if len(gml_content) > 500 else gml_content
                    logger.debug(f"GML sample: {sample}")

        except Exception as e:
            logger.warning(f"Geodienste.ch WMS fallback failed for coordinates {x}, {y}: {e}")

        return None

    def _extract_from_gml(self, gml_content: str, field_name: str) -> Optional[str]:
        """Extract a field value from GML content using regex."""
        import re
        pattern = f'<{field_name}[^>]*>([^<]+)</{field_name}>'
        match = re.search(pattern, gml_content)
        return match.group(1).strip() if match else None

    def _derive_canton_from_coordinates(self, x: float, y: float) -> Optional[str]:
        """
        Simple coordinate-based canton detection for fallback when APIs don't provide canton info.
        Only used when geodienste.ch WMS provides EGRID but no canton information.
        """
        # Simple coordinate ranges for cantons that commonly need fallback
        # Based on our test data showing NE, VD, JU need fallback
        if 2550000 <= x <= 2580000 and 1200000 <= y <= 1220000:
            return 'NE'  # Neuchâtel
        elif 2520000 <= x <= 2550000 and 1150000 <= y <= 1170000:
            return 'VD'  # Vaud
        elif 2570000 <= x <= 2590000 and 1240000 <= y <= 1260000:
            return 'JU'  # Jura
        elif 2490000 <= x <= 2510000 and 1110000 <= y <= 1130000:
            return 'GE'  # Geneva (backup)
        elif 2560000 <= x <= 2590000 and 1170000 <= y <= 1190000:
            return 'FR'  # Fribourg (backup)

        logger.warning(f"Could not derive canton from coordinates {x}, {y}")
        return None

    async def get_oereb_data(self, egrid: str, canton: str) -> Optional[Dict[str, Any]]:
        """
        Get ÖREB data for a specific EGRID from the appropriate cantonal service.

        Args:
            egrid: The property identifier (EGRID)
            canton: Canton abbreviation (e.g., 'ZH', 'BE')

        Returns:
            Dict containing parsed ÖREB data, or None if not available
        """
        if canton not in CANTONAL_OEREB_SERVICES:
            raise HTTPException(
                status_code=404,
                detail=f"ÖREB-Service für Kanton {canton} nicht verfügbar"
            )

        base_url = CANTONAL_OEREB_SERVICES[canton]

        try:
            # Check if canton has special URL patterns
            if canton in SPECIAL_ENDPOINTS:
                # Use special endpoint URL with timestamp for cantons that need it (like BL)
                timestamp = str(int(time.time() * 1000))
                url = SPECIAL_ENDPOINTS[canton]['xml'].format(egrid=egrid, timestamp=timestamp)
                params = None  # Special endpoints have parameters in the URL already
                logger.info(f"Using SPECIAL endpoint for {canton}: {url}")
            else:
                # Use standard URL pattern
                url = f"{base_url}/extract/xml/"
                params = {
                    'EGRID': egrid,
                    'LANG': 'de',
                    'WITHIMAGES': 'true'
                }
                logger.info(f"Using STANDARD endpoint for {canton}: {url} with params {params}")

            async with httpx.AsyncClient(timeout=self.timeout, follow_redirects=True) as client:
                # Debug logging and request
                if params is None:
                    logger.info(f"Making ÖREB request for {canton} - URL: {url} (no params)")
                    response = await client.get(url)
                else:
                    logger.info(f"Making ÖREB request for {canton} - URL: {url}, params: {params}")
                    response = await client.get(url, params=params)

                logger.info(f"ÖREB response for {canton} - Status: {response.status_code}, Content-Type: {response.headers.get('content-type')}")
                response.raise_for_status()

                # Parse XML response
                xml_content = response.text

                # Check if response is actually XML
                if not xml_content.strip().startswith('<?xml') and not xml_content.strip().startswith('<'):
                    logger.error(f"Non-XML response from {canton} ÖREB service for EGRID {egrid}: {xml_content[:200]}...")
                    return None

                return await self._parse_oereb_xml(xml_content, egrid)

        except httpx.TimeoutException:
            logger.error(f"Timeout while fetching ÖREB data for EGRID {egrid} in canton {canton}")
            raise HTTPException(status_code=408, detail="Zeitüberschreitung beim Abrufen der ÖREB-Daten")
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                return None  # No ÖREB data available for this property
            elif e.response.status_code == 400:
                # Bad request - likely invalid EGRID or parameters
                logger.error(f"Bad request to ÖREB service for {canton} with EGRID {egrid}: {e}")
                raise HTTPException(
                    status_code=502,
                    detail=f"Ungültige Anfrage an ÖREB-Service für Kanton {canton} (EGRID: {egrid})"
                )
            elif e.response.status_code in [500, 502, 503, 504]:
                # External service error - provide more specific message
                logger.error(f"External service error while fetching ÖREB data for {canton}: {e}")
                raise HTTPException(
                    status_code=502,
                    detail=f"Der ÖREB-Service für Kanton {canton} ist temporär nicht verfügbar (HTTP {e.response.status_code})"
                )
            else:
                logger.error(f"HTTP error while fetching ÖREB data: {e}")
                raise HTTPException(status_code=502, detail="Fehler beim Abrufen der ÖREB-Daten")
        except Exception as e:
            logger.error(f"Unexpected error while fetching ÖREB data: {e}")
            raise HTTPException(status_code=500, detail="Unerwarteter Fehler beim Abrufen der ÖREB-Daten")

    async def _parse_oereb_xml(self, xml_content: str, egrid: str) -> Dict[str, Any]:
        """
        Parse ÖREB XML response into comprehensive structured data.
        This method is designed to be flexible and extract ALL information from any ÖREB XML format.

        Args:
            xml_content: Raw XML content from ÖREB service
            egrid: The EGRID for reference

        Returns:
            Dict containing comprehensive ÖREB data
        """
        try:
            root = ET.fromstring(xml_content)

            # Auto-detect namespaces from the XML
            namespaces = self._extract_namespaces(root)

            # Log the detected namespaces for debugging
            logger.info(f"Detected namespaces for EGRID {egrid}: {list(namespaces.keys())}")

            # Try multiple namespace combinations for maximum compatibility
            namespace_variants = [
                namespaces,  # Auto-detected
                {'extract': 'http://schemas.geo.admin.ch/V_D/OeREB/2.0/Extract', 'data': 'http://schemas.geo.admin.ch/V_D/OeREB/2.0/ExtractData'},
                {'extract': 'http://schemas.geo.admin.ch/V_D/OeREB/1.0/Extract', 'data': 'http://schemas.geo.admin.ch/V_D/OeREB/1.0/ExtractData'},
                {},  # No namespaces (fallback)
            ]

            # Initialize result with comprehensive structure
            result = {
                'egrid': egrid,
                'raw_xml_info': {},  # Store all extracted XML data
                'creation_date': '',
                'extract_identifier': '',
                'concerned_themes': [],
                'not_concerned_themes': [],
                'themes_without_data': [],
                'property_info': {},
                'restrictions': [],
                'legal_provisions': [],
                'documents': [],
                'general_information': [],
                'base_data': [],
                'glossary': [],
                'municipality': {},
                'responsible_office': {},
                'logo_ref': '',
                'certification': '',
                'certification_at_web': '',
                'qr_code': '',
                'all_elements': []  # Store all found elements for debugging
            }

            # Extract ALL information using flexible parsing
            for ns_variant in namespace_variants:
                try:
                    # Try to extract with this namespace variant
                    extracted_data = self._extract_all_xml_data(root, ns_variant, egrid)

                    # Merge extracted data into result
                    for key, value in extracted_data.items():
                        if value:  # Only update if we found something
                            if isinstance(value, list) and len(value) > 0:
                                result[key] = value
                            elif isinstance(value, dict) and value:
                                result[key] = value
                            elif isinstance(value, str) and value.strip():
                                result[key] = value

                    # If we found substantial data, break
                    if (result['property_info'] or result['restrictions'] or
                        result['concerned_themes'] or result['not_concerned_themes']):
                        break

                except Exception as e:
                    logger.debug(f"Failed to parse with namespace variant {ns_variant}: {e}")
                    continue

            # Post-process to ensure compatibility with tests
            self._post_process_oereb_data(result)

            # Store raw XML for debugging if needed
            result['raw_xml_info']['total_elements'] = len(list(root.iter()))
            result['raw_xml_info']['root_tag'] = root.tag
            result['raw_xml_info']['namespaces_used'] = list(namespaces.keys()) if namespaces else []

            return result

        except ET.ParseError as e:
            logger.error(f"XML parsing error for EGRID {egrid}: {e}")
            # Raise HTTPException for invalid XML
            from fastapi import HTTPException
            raise HTTPException(
                status_code=502,
                detail="Fehler beim Verarbeiten der ÖREB-Daten"
            )
        except Exception as e:
            logger.error(f"Unexpected error parsing ÖREB XML for EGRID {egrid}: {e}")
            # Raise HTTPException for other parsing errors
            from fastapi import HTTPException
            raise HTTPException(
                status_code=502,
                detail="Fehler beim Verarbeiten der ÖREB-Daten"
            )

    def _post_process_oereb_data(self, result):
        """Post-process ÖREB data to ensure compatibility with tests and UI expectations."""
        # Ensure municipality is in property_info for test compatibility
        if result.get('municipality', {}).get('name') and 'property_info' in result:
            result['property_info']['municipality'] = result['municipality']['name']

        # Extract municipality from structured data if available
        if (result.get('structured_data', {}).get('real_estate', {}).get('municipality_name')
            and 'property_info' in result):
            result['property_info']['municipality'] = result['structured_data']['real_estate']['municipality_name']

        # Ensure area is properly mapped
        if (result.get('structured_data', {}).get('real_estate', {}).get('land_registry_area')
            and 'property_info' in result):
            result['property_info']['area'] = result['structured_data']['real_estate']['land_registry_area']

        # Ensure type is properly mapped
        if (result.get('structured_data', {}).get('real_estate', {}).get('type')
            and 'property_info' in result):
            result['property_info']['type'] = result['structured_data']['real_estate']['type']

        # Ensure number is properly mapped
        if (result.get('structured_data', {}).get('real_estate', {}).get('number')
            and 'property_info' in result):
            result['property_info']['number'] = result['structured_data']['real_estate']['number']

        # Process restrictions to ensure proper field mapping
        for restriction in result.get('restrictions', []):
            # Extract localized text from complex structures
            self._flatten_localized_fields(restriction)

            # Map theme to topic for compatibility
            if 'Theme' in restriction and 'topic' not in restriction:
                restriction['topic'] = self._extract_text_from_complex_field(restriction['Theme'])

            # Map LegendText to legend_text for compatibility
            if 'LegendText' in restriction and 'legend_text' not in restriction:
                restriction['legend_text'] = self._extract_text_from_complex_field(restriction['LegendText'])

            # Map TypeCode to type_code for compatibility
            if 'TypeCode' in restriction and 'type_code' not in restriction:
                restriction['type_code'] = self._extract_text_from_complex_field(restriction['TypeCode'])

            # Map LawStatus to lawstatus for compatibility
            if 'LawStatus' in restriction and 'lawstatus' not in restriction:
                restriction['lawstatus'] = self._extract_text_from_complex_field(restriction['LawStatus'])

            # Map AreaShare to area for compatibility
            if 'AreaShare' in restriction and 'area' not in restriction:
                restriction['area'] = self._extract_text_from_complex_field(restriction['AreaShare'])

            # Map PartInPercent to part_in_percent for compatibility
            if 'PartInPercent' in restriction and 'part_in_percent' not in restriction:
                restriction['part_in_percent'] = self._extract_text_from_complex_field(restriction['PartInPercent'])

        # Process legal provisions to ensure proper field mapping
        for provision in result.get('legal_provisions', []):
            # Map Title to title for compatibility
            if 'Title' in provision and 'title' not in provision:
                provision['title'] = provision['Title']

            # Map Abbreviation to abbreviation for compatibility
            if 'Abbreviation' in provision and 'abbreviation' not in provision:
                provision['abbreviation'] = provision['Abbreviation']

            # Map OfficialNumber to number for compatibility
            if 'OfficialNumber' in provision and 'number' not in provision:
                provision['number'] = provision['OfficialNumber']

            # Map TextAtWeb to text_at_web for compatibility
            if 'TextAtWeb' in provision and 'text_at_web' not in provision:
                provision['text_at_web'] = provision['TextAtWeb']

        # Process documents to ensure proper field mapping
        for document in result.get('documents', []):
            # Map Title to title for compatibility
            if 'Title' in document and 'title' not in document:
                document['title'] = document['Title']

            # Map Type to type for compatibility
            if 'Type' in document and 'type' not in document:
                document['type'] = document['Type']

            # Map OfficialNumber to official_number for compatibility
            if 'OfficialNumber' in document and 'official_number' not in document:
                document['official_number'] = document['OfficialNumber']

            # Map TextAtWeb to web_reference for compatibility
            if 'TextAtWeb' in document and 'web_reference' not in document:
                document['web_reference'] = document['TextAtWeb']

    def _extract_text_from_complex_field(self, field_value):
        """Extract simple text from complex nested field structures."""
        if isinstance(field_value, str):
            return field_value

        if isinstance(field_value, dict):
            # Try to extract from LocalisedText structure
            if 'LocalisedText' in field_value:
                localized = field_value['LocalisedText']
                if isinstance(localized, dict) and 'Text' in localized:
                    text_value = localized['Text']
                    if isinstance(text_value, list) and len(text_value) > 0:
                        return str(text_value[0])
                    elif isinstance(text_value, str):
                        return text_value

            # Try to extract from direct text field
            if 'text' in field_value:
                return str(field_value['text'])

            # Try to extract any string value
            for key, value in field_value.items():
                if isinstance(value, str) and value.strip():
                    return value
                elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], str):
                    return value[0]

        if isinstance(field_value, list) and len(field_value) > 0:
            return str(field_value[0])

        return str(field_value) if field_value else ''

    def _flatten_localized_fields(self, data_dict):
        """Flatten complex localized text structures in a dictionary."""
        for key, value in list(data_dict.items()):
            if isinstance(value, dict) and 'LocalisedText' in value:
                # Extract the text from LocalisedText structure
                text_value = self._extract_text_from_complex_field(value)
                if text_value:
                    data_dict[key] = text_value

    def _extract_namespaces(self, root):
        """Extract all namespaces from XML root element."""
        namespaces = {}
        for prefix, uri in root.nsmap.items() if hasattr(root, 'nsmap') else {}:
            if prefix:
                namespaces[prefix] = uri
            else:
                # Default namespace
                namespaces['default'] = uri

        # Add common ÖREB namespaces if not found
        if 'data' not in namespaces:
            for uri in [
                'http://schemas.geo.admin.ch/V_D/OeREB/2.0/ExtractData',
                'http://schemas.geo.admin.ch/V_D/OeREB/1.0/ExtractData'
            ]:
                if uri in str(root):
                    namespaces['data'] = uri
                    break

        return namespaces

    def _extract_all_xml_data(self, root, namespaces, egrid):
        """Extract all possible data from XML using flexible parsing following ÖREB schema."""
        result = {
            'creation_date': '',
            'extract_identifier': '',
            'concerned_themes': [],
            'not_concerned_themes': [],
            'themes_without_data': [],
            'property_info': {},
            'restrictions': [],
            'legal_provisions': [],
            'documents': [],
            'general_information': [],
            'base_data': [],
            'glossary': [],
            'municipality': {},
            'responsible_office': {},
            'logo_ref': '',
            'certification': '',
            'certification_at_web': '',
            'qr_code': '',
            'all_elements': [],
            'structured_data': {}  # Better structured data following ÖREB schema
        }

        # Extract all elements for comprehensive display
        all_elements = []
        for elem in root.iter():
            if elem.text and elem.text.strip():
                element_info = {
                    'tag': elem.tag,
                    'text': elem.text.strip(),
                    'attrib': dict(elem.attrib) if elem.attrib else {}
                }
                all_elements.append(element_info)

        result['all_elements'] = all_elements

        # Try multiple search patterns for each data type
        search_patterns = {
            'creation_date': [
                './/CreationDate', './/data:CreationDate', './/extract:CreationDate',
                './/*[contains(local-name(), "CreationDate")]', './/*[contains(local-name(), "Date")]'
            ],
            'extract_identifier': [
                './/ExtractIdentifier', './/data:ExtractIdentifier', './/extract:ExtractIdentifier',
                './/*[contains(local-name(), "ExtractIdentifier")]', './/*[contains(local-name(), "Identifier")]'
            ],
            'egrid': [
                './/EGRID', './/data:EGRID', './/extract:EGRID',
                './/*[contains(local-name(), "EGRID")]'
            ],
            'municipality': [
                './/MunicipalityName', './/data:MunicipalityName', './/Municipality',
                './/*[contains(local-name(), "Municipality")]'
            ]
        }

        # Extract basic information using flexible patterns
        for field, patterns in search_patterns.items():
            for pattern in patterns:
                try:
                    elements = root.findall(pattern, namespaces) if namespaces else root.findall(pattern)
                    if elements:
                        for elem in elements:
                            if elem.text and elem.text.strip():
                                if field == 'municipality':
                                    result['municipality']['name'] = elem.text.strip()
                                elif field == 'egrid':
                                    if not result['property_info']:
                                        result['property_info'] = {}
                                    result['property_info']['egrid'] = elem.text.strip()
                                else:
                                    result[field] = elem.text.strip()
                                break
                    if result.get(field):
                        break
                except Exception:
                    continue

        # Extract themes using flexible patterns
        theme_patterns = [
            './/ConcernedTheme', './/data:ConcernedTheme',
            './/*[contains(local-name(), "ConcernedTheme")]',
            './/*[contains(local-name(), "Theme")]'
        ]

        for pattern in theme_patterns:
            try:
                themes = root.findall(pattern, namespaces) if namespaces else root.findall(pattern)
                for theme in themes:
                    theme_data = self._extract_element_data(theme, namespaces)
                    if theme_data:
                        result['concerned_themes'].append(theme_data)
                if result['concerned_themes']:
                    break
            except Exception:
                continue

        # Extract property information using flexible patterns
        property_patterns = [
            './/RealEstate', './/data:RealEstate', './/Property',
            './/*[contains(local-name(), "RealEstate")]',
            './/*[contains(local-name(), "Property")]'
        ]

        for pattern in property_patterns:
            try:
                properties = root.findall(pattern, namespaces) if namespaces else root.findall(pattern)
                for prop in properties:
                    prop_data = self._extract_element_data(prop, namespaces)
                    if prop_data:
                        result['property_info'].update(prop_data)
                if result['property_info']:
                    break
            except Exception:
                continue

        # Extract restrictions using flexible patterns
        restriction_patterns = [
            './/RestrictionOnLandownership', './/data:RestrictionOnLandownership',
            './/Restriction', './/*[contains(local-name(), "Restriction")]'
        ]

        for pattern in restriction_patterns:
            try:
                restrictions = root.findall(pattern, namespaces) if namespaces else root.findall(pattern)
                for restriction in restrictions:
                    restriction_data = self._extract_element_data(restriction, namespaces)
                    if restriction_data:
                        result['restrictions'].append(restriction_data)
                if result['restrictions']:
                    break
            except Exception:
                continue

        # Extract structured data following ÖREB schema
        result['structured_data'] = self._extract_structured_oereb_data(root, namespaces)

        # Extract logos and images
        result['logos'] = self._extract_logos_and_images(root, namespaces)

        return result

    def _extract_structured_oereb_data(self, root, namespaces):
        """Extract data following the official ÖREB schema structure."""
        structured = {
            'extract_info': {},
            'real_estate': {},
            'restrictions': [],
            'themes': {
                'concerned': [],
                'not_concerned': [],
                'without_data': []
            },
            'legal_provisions': [],
            'documents': [],
            'general_information': [],
            'glossary': [],
            'responsible_office': {},
            'disclaimers': []
        }

        # Extract basic extract information
        structured['extract_info'] = {
            'creation_date': self._find_text_flexible(root, [
                './/CreationDate', './/data:CreationDate', './/*[contains(local-name(), "CreationDate")]'
            ], namespaces),
            'extract_identifier': self._find_text_flexible(root, [
                './/ExtractIdentifier', './/data:ExtractIdentifier', './/*[contains(local-name(), "ExtractIdentifier")]'
            ], namespaces),
            'update_date': self._find_text_flexible(root, [
                './/UpdateDateCS', './/data:UpdateDateCS', './/*[contains(local-name(), "UpdateDate")]'
            ], namespaces)
        }

        # Extract real estate information following schema
        real_estate_patterns = [
            './/RealEstate', './/data:RealEstate', './/*[contains(local-name(), "RealEstate")]'
        ]

        for pattern in real_estate_patterns:
            try:
                real_estate_elem = root.find(pattern, namespaces) if namespaces else root.find(pattern)
                if real_estate_elem is not None:
                    structured['real_estate'] = {
                        'egrid': self._find_text_flexible(real_estate_elem, [
                            './/EGRID', './/data:EGRID', './/*[contains(local-name(), "EGRID")]'
                        ], namespaces),
                        'number': self._find_text_flexible(real_estate_elem, [
                            './/Number', './/data:Number', './/*[contains(local-name(), "Number")]'
                        ], namespaces),
                        'municipality_name': self._find_text_flexible(real_estate_elem, [
                            './/MunicipalityName', './/data:MunicipalityName', './/*[contains(local-name(), "Municipality")]'
                        ], namespaces),
                        'municipality_code': self._find_text_flexible(real_estate_elem, [
                            './/MunicipalityCode', './/data:MunicipalityCode', './/*[contains(local-name(), "MunicipalityCode")]'
                        ], namespaces),
                        'canton': self._find_text_flexible(real_estate_elem, [
                            './/Canton', './/data:Canton', './/*[contains(local-name(), "Canton")]'
                        ], namespaces),
                        'land_registry_area': self._find_text_flexible(real_estate_elem, [
                            './/LandRegistryArea', './/data:LandRegistryArea', './/*[contains(local-name(), "Area")]'
                        ], namespaces),
                        'type': self._find_text_flexible(real_estate_elem, [
                            './/Type//Text', './/data:Type//data:Text', './/Type//data:Text',
                            './/Type', './/data:Type', './/*[contains(local-name(), "Type")]'
                        ], namespaces) or self._extract_localized_text_flexible(
                            real_estate_elem.find('.//Type', namespaces) if namespaces else real_estate_elem.find('.//Type'),
                            namespaces
                        )
                    }
                    break
            except Exception:
                continue

        return structured

    def _extract_logos_and_images(self, root, namespaces):
        """Extract all logos and images from ÖREB XML."""
        logos = {
            'plr_cadastre_logo': None,
            'federal_logo': None,
            'cantonal_logo': None,
            'municipality_logo': None,
            'qr_code': None,
            'symbols': [],
            'maps': []
        }

        # Extract main logos using flexible patterns
        logo_patterns = {
            'plr_cadastre_logo': [
                './/LogoPLRCadastre', './/data:LogoPLRCadastre',
                './/*[contains(local-name(), "LogoPLRCadastre")]'
            ],
            'federal_logo': [
                './/FederalLogo', './/data:FederalLogo',
                './/*[contains(local-name(), "FederalLogo")]'
            ],
            'cantonal_logo': [
                './/CantonalLogo', './/data:CantonalLogo',
                './/*[contains(local-name(), "CantonalLogo")]'
            ],
            'municipality_logo': [
                './/MunicipalityLogo', './/data:MunicipalityLogo',
                './/*[contains(local-name(), "MunicipalityLogo")]'
            ],
            'qr_code': [
                './/QRCode', './/data:QRCode',
                './/*[contains(local-name(), "QRCode")]'
            ]
        }

        # Extract each logo type
        for logo_type, patterns in logo_patterns.items():
            for pattern in patterns:
                try:
                    logo_elem = root.find(pattern, namespaces) if namespaces else root.find(pattern)
                    if logo_elem is not None:
                        # Try to find base64 data in different ways
                        base64_data = self._extract_base64_data(logo_elem, namespaces)
                        if base64_data:
                            logos[logo_type] = {
                                'data': base64_data,
                                'format': self._detect_image_format(base64_data)
                            }
                            break
                except Exception:
                    continue

        # Extract symbols from legend entries and restrictions
        symbol_patterns = [
            './/Symbol', './/data:Symbol',
            './/*[contains(local-name(), "Symbol")]'
        ]

        for pattern in symbol_patterns:
            try:
                symbols = root.findall(pattern, namespaces) if namespaces else root.findall(pattern)
                for symbol in symbols:
                    base64_data = self._extract_base64_data(symbol, namespaces)
                    if base64_data:
                        logos['symbols'].append({
                            'data': base64_data,
                            'format': self._detect_image_format(base64_data),
                            'context': self._get_symbol_context(symbol)
                        })
            except Exception:
                continue

        # Extract map images
        map_patterns = [
            './/Image', './/data:Image',
            './/*[contains(local-name(), "Image")]'
        ]

        for pattern in map_patterns:
            try:
                maps = root.findall(pattern, namespaces) if namespaces else root.findall(pattern)
                for map_elem in maps:
                    # Maps might have multilingual images
                    multilingual_images = self._extract_multilingual_images(map_elem, namespaces)
                    if multilingual_images:
                        logos['maps'].extend(multilingual_images)
            except Exception:
                continue

        return logos

    def _extract_base64_data(self, element, namespaces):
        """Extract base64 data from an element."""
        if element is None:
            return None

        # Try direct text content
        if element.text and element.text.strip():
            return element.text.strip()

        # Try Blob child element
        blob_patterns = [
            './/Blob', './/data:Blob', './/*[contains(local-name(), "Blob")]'
        ]

        for pattern in blob_patterns:
            try:
                blob_elem = element.find(pattern, namespaces) if namespaces else element.find(pattern)
                if blob_elem is not None and blob_elem.text:
                    return blob_elem.text.strip()
            except Exception:
                continue

        return None

    def _detect_image_format(self, base64_data):
        """Detect image format from base64 data."""
        if not base64_data:
            return 'unknown'

        # Decode first few bytes to check magic numbers
        try:
            decoded = base64.b64decode(base64_data[:20])

            if decoded.startswith(b'\x89PNG'):
                return 'png'
            elif decoded.startswith(b'\xff\xd8\xff'):
                return 'jpeg'
            elif decoded.startswith(b'GIF8'):
                return 'gif'
            elif decoded.startswith(b'<svg') or b'<svg' in decoded:
                return 'svg'
            elif decoded.startswith(b'%PDF'):
                return 'pdf'
        except Exception:
            pass

        return 'unknown'

    def _get_symbol_context(self, symbol_element):
        """Get context information for a symbol (what it represents)."""
        parent = symbol_element.getparent() if hasattr(symbol_element, 'getparent') else None
        if parent is not None:
            # Try to find legend text or type code
            context_patterns = [
                './/LegendText', './/TypeCode', './/Theme',
                './/data:LegendText', './/data:TypeCode', './/data:Theme'
            ]

            for pattern in context_patterns:
                try:
                    context_elem = parent.find(pattern)
                    if context_elem is not None:
                        if hasattr(context_elem, 'text') and context_elem.text:
                            return context_elem.text.strip()
                        # For multilingual text, try to extract
                        text = self._extract_localized_text_flexible(context_elem, {})
                        if text:
                            return text
                except Exception:
                    continue

        return 'Symbol'

    def _extract_multilingual_images(self, element, namespaces):
        """Extract multilingual images (for maps)."""
        images = []

        # Look for LocalisedBlob elements
        blob_patterns = [
            './/LocalisedBlob', './/data:LocalisedBlob',
            './/*[contains(local-name(), "LocalisedBlob")]'
        ]

        for pattern in blob_patterns:
            try:
                blobs = element.findall(pattern, namespaces) if namespaces else element.findall(pattern)
                for blob in blobs:
                    language = self._find_text_flexible(blob, [
                        './/Language', './/data:Language'
                    ], namespaces)

                    base64_data = self._extract_base64_data(blob, namespaces)
                    if base64_data:
                        images.append({
                            'data': base64_data,
                            'format': self._detect_image_format(base64_data),
                            'language': language or 'unknown',
                            'type': 'map'
                        })
            except Exception:
                continue

        return images

    def _find_text_flexible(self, element, patterns, namespaces):
        """Find text using multiple XPath patterns."""
        for pattern in patterns:
            try:
                found = element.find(pattern, namespaces) if namespaces else element.find(pattern)
                if found is not None and found.text:
                    return found.text.strip()
            except Exception:
                continue
        return ''

    def _extract_element_data(self, element, namespaces):
        """Extract all data from an XML element recursively."""
        data = {}

        # Get direct text content
        if element.text and element.text.strip():
            data['text'] = element.text.strip()

        # Get attributes
        if element.attrib:
            data['attributes'] = dict(element.attrib)

        # Get all child elements
        for child in element:
            child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag

            if child.text and child.text.strip():
                # Handle localized text specially
                if 'LocalisedText' in child.tag or 'Text' in child.tag:
                    localized_data = self._extract_localized_text_flexible(child, namespaces)
                    if localized_data:
                        data[child_tag] = localized_data
                else:
                    data[child_tag] = child.text.strip()

            # Recursively extract from children
            child_data = self._extract_element_data(child, namespaces)
            if child_data:
                if child_tag in data:
                    if not isinstance(data[child_tag], list):
                        data[child_tag] = [data[child_tag]]
                    data[child_tag].append(child_data)
                else:
                    data[child_tag] = child_data

        return data

    def _extract_localized_text_flexible(self, element, namespaces, language='de'):
        """Extract localized text with maximum flexibility, supporting multiple languages."""
        if element is None:
            return ''

        # Try multiple languages in order of preference
        languages_to_try = [language, 'de', 'fr', 'it', 'rm', 'en']

        # Try multiple approaches to find text
        approaches = [
            # Standard ÖREB localized text with multiple languages
            lambda: self._extract_localized_text_multilingual(element, namespaces, languages_to_try),
            # Direct text content
            lambda: element.text.strip() if element.text else '',
            # First child text
            lambda: next((child.text.strip() for child in element if child.text and child.text.strip()), ''),
            # Any descendant with text
            lambda: next((desc.text.strip() for desc in element.iter() if desc.text and desc.text.strip()), '')
        ]

        for approach in approaches:
            try:
                result = approach()
                if result:
                    return result
            except Exception:
                continue

        return ''

    def _extract_localized_text_multilingual(self, element, namespaces, languages):
        """Extract localized text trying multiple languages."""
        for lang in languages:
            try:
                result = self._extract_localized_text(element, namespaces, lang)
                if result:
                    return result
            except Exception:
                continue
        return ''

    def _extract_localized_text(self, element, namespaces, language='de'):
        """
        Extract localized text from ÖREB XML elements.

        Args:
            element: XML element containing localized text
            namespaces: XML namespaces
            language: Language code (default: 'de')

        Returns:
            Extracted text or empty string if not found
        """
        if element is None:
            return ''

        # Try to find localized text for the specified language
        localized_text = element.find(f'.//data:LocalisedText[data:Language="{language}"]', namespaces)
        if localized_text is not None:
            text_elem = localized_text.find('.//data:Text', namespaces)
            if text_elem is not None:
                return text_elem.text or ''

        # Fallback: try to find any localized text
        localized_text = element.find('.//data:LocalisedText', namespaces)
        if localized_text is not None:
            text_elem = localized_text.find('.//data:Text', namespaces)
            if text_elem is not None:
                return text_elem.text or ''

        # Fallback: try to get direct text content
        code_elem = element.find('.//data:Code', namespaces)
        if code_elem is not None:
            return code_elem.text or ''

        return ''
